import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'dart:ui' as ui;

import 'package:client/bloc/targets_blocs.dart';
import 'package:client/bloc/web_socket_bloc.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:path_provider/path_provider.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';
import 'package:utils/utils.dart';

import '../../bloc/wy_device_blocs.dart';
import '../components/roi_selector/multi_camera_roi_selector.dart';

class ViewImage extends StatefulWidget {
  final context;

  const ViewImage({
    super.key,
    required this.context,
  });

  @override
  State<ViewImage> createState() => _ViewImageState();
}

class _ViewImageState extends State<ViewImage> {
  String date = "";
  bool _showROIs = true;
  int _pwm = 2000;

  // 管理当前查看的图片时间戳
  DateTime _dateTimeInner = DateTime.now();

  DateTime get _dateTime => _dateTimeInner;

  // 每次更新时间戳都会加载 最早 不小于 该时间戳的历史图片
  // 如果该时间戳大于所有历史图片，将在加载最后一张
  set _dateTime(DateTime newValue) {
    _dateTimeInner = newValue;
    updateImage();
  }

  late WyDeviceBloc wyDeviceBloc;
  ui.Image? _currentImage; // 添加图片变量

  StreamSubscription? _webSocketSubscription;
  StreamSubscription? _wyDeviceSubscription;

  // 其中 List<int> 中的 int 是毫秒时间戳
  final Map<int, List<int>> _imageHistory = {};
  final Set<int> _loaddedHistoryImageCamera = {};

  Future<List<int>> getCameraImageHistory(int cameraId) async {
    if (_loaddedHistoryImageCamera.contains(cameraId)) {
      return _imageHistory[cameraId] ?? [];
    }

    _loaddedHistoryImageCamera.add(cameraId);

    if (_imageHistory[cameraId] == null) {
      _imageHistory[cameraId] = [];
    }

    final directory = await getApplicationDocumentsDirectory();
    String deviceId = wyDeviceBloc.state.deviceId;
    String savePath = "${directory.path}/cameraImage/$deviceId/$cameraId";
    final dir = Directory(savePath);

    final entity = await dir.list().toList();
    final List<int> images = [];
    for (var e in entity) {
      if (e is File) {
        final timeStamp = int.tryParse(e.path.split("_").last.split(".").first);
        if (timeStamp != null) {
          images.add(timeStamp);
        }
      }
    }
    _imageHistory[cameraId]?.addAll(images);
    _imageHistory[cameraId]?.sort();
    return _imageHistory[cameraId] ?? [];
  }

  Future<void> updateImage() async {
    try {
      final cameraId = wyDeviceBloc.state.selectedCameraId;
      final history = await getCameraImageHistory(cameraId);

      if (history.isEmpty) {
        debugPrint('更新图片失败: 没有历史图片');
        return;
      }

      final idx = min(lowerBound(history, _dateTime.millisecondsSinceEpoch),
          history.length - 1);
      final imageTimestamp = history[idx];

      final directory = await getApplicationDocumentsDirectory();

      isOutdated() {
        final idx2 = min(lowerBound(history, _dateTime.millisecondsSinceEpoch),
            history.length - 1);
        return cameraId != wyDeviceBloc.state.selectedCameraId ||
            imageTimestamp != history[idx2];
      }

      if (isOutdated()) {
        return;
      }

      String deviceId = wyDeviceBloc.state.deviceId;
      String savePath = "${directory.path}/cameraImage/$deviceId/$cameraId";
      final file = File("$savePath/${cameraId}_image_$imageTimestamp.jpg");

      final Uint8List bytes = await file.readAsBytes();
      if (isOutdated()) {
        return;
      }
      final ui.Codec codec = await ui.instantiateImageCodec(bytes);
      if (isOutdated()) {
        return;
      }
      final ui.FrameInfo frameInfo = await codec.getNextFrame();
      if (isOutdated()) {
        return;
      }

      setState(() {
        _currentImage = frameInfo.image;
        // 直接修改内部值，不触发更新
        _dateTimeInner = DateTime.fromMillisecondsSinceEpoch(imageTimestamp);
      });
    } catch (e) {
      debugPrint('更新图片失败: $e');
    }
  }

  @override
  void initState() {
    super.initState();
    wyDeviceBloc = context.read<WyDeviceBloc>();
    _loadSampleImage(); // 加载示例图片
    var webSocketBloc = context.read<WebSocketBloc>();
    _webSocketSubscription = webSocketBloc.stream.listen((state) {
      if (state is WebSocketMessageReceived) {
        _handleWebSocketMessage(state.message);
      }
    });

    var currentSelectedCameraId = wyDeviceBloc.state.selectedCameraId;
    _wyDeviceSubscription = wyDeviceBloc.stream.listen((state) {
      // 切换摄像头的时候更新一下图像
      if (currentSelectedCameraId != state.selectedCameraId) {
        currentSelectedCameraId = state.selectedCameraId;
        debugPrint("监听到切换摄像头事件");
        updateImage();
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
    _webSocketSubscription?.cancel();
    _wyDeviceSubscription?.cancel();
  }

  void _handleWebSocketMessage(Map<String, dynamic> message) {
    if (message.containsKey('Telemetry') &&
        message['Telemetry'] is Map<String, dynamic> &&
        (message['Telemetry'] as Map<String, dynamic>)
            .containsKey('ImageData')) {
      _processImageData(message['Telemetry']['ImageData']);
    }
  }

  Future<void> _processImageData(Map<String, dynamic> data) async {
    try {
      var imageBase64 = data['base64'];
      int cameraId = data["cameraId"];

      //解析base64字符串
      Uint8List imageBytes = base64.decode(imageBase64);
      // 将字节数据解码为图片
      final ui.Codec codec = await ui.instantiateImageCodec(imageBytes);
      final ui.FrameInfo frameInfo = await codec.getNextFrame();

      // 将图像保存到本地
      try {
        // 获取临时目录路径
        final directory = await getApplicationDocumentsDirectory();
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        String deviceId = wyDeviceBloc.state.deviceId;

        String savePath = "${directory.path}/cameraImage/$deviceId/$cameraId";
        // 检查目录是否存在，如果不存在则创建
        final dir = Directory(savePath);
        if (!await dir.exists()) {
          await dir.create(recursive: true);
        }

        final filePath = '$savePath/${cameraId}_image_$timestamp.jpg';

        wyDeviceBloc.add(
          DevicePictureUpdate(
            cameraId,
            filePath,
          ),
        );

        // 将图像写入文件
        final file = File(filePath);
        await file.writeAsBytes(imageBytes);
        debugPrint('图像已保存到: $filePath');

        final history = _imageHistory[cameraId];
        if (history != null) {
          history.add(timestamp);
          history.sort();
        } else {
          _imageHistory[cameraId] = [timestamp];
        }
        if (wyDeviceBloc.state.selectedCameraId == cameraId) {
          setState(() {
            _dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
          });
        }
      } catch (saveError) {
        debugPrint('保存图像到本地失败: $saveError');
      }
    } catch (e) {
      debugPrint('处理图片数据时出错: $e');
    }
  }

  // 异步加载图片资源
  Future<void> _loadSampleImage() async {
    await updateImage();
    if (_currentImage != null) {
      return;
    }
    try {
      final ByteData data =
          await rootBundle.load('assets/images/sample_image.jpg');
      final Uint8List bytes = data.buffer.asUint8List();
      final ui.Codec codec = await ui.instantiateImageCodec(bytes);
      final ui.FrameInfo frameInfo = await codec.getNextFrame();
      if (_currentImage == null) {
        setState(() {
          _currentImage = frameInfo.image;
        });
      }
    } catch (e) {
      debugPrint('加载示例图片失败: $e');
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          title: const Text('查看图像'),
        ),
        body: Column(
          children: [
            AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              height: 320, // 固定高度
              child: AnimatedOpacity(
                opacity: 1.0,
                duration: const Duration(milliseconds: 200),
                child: _buildSwitchableSection(context),
              ),
            ),
            const SizedBox(
              height: 12,
            ),
            _buildHistoryPanel(),
            const SizedBox(
              height: 12,
            ),
            _buildOptionRow(Text("当前摄像头"),
                BlocBuilder<WyDeviceBloc, WyDeviceState>(
                    builder: (context, state) {
              // 将来这里必改
              const cameraCount = 2;

              final radios = Iterable.generate(
                  cameraCount,
                  (index) => TDRadio(
                        id: index.toString(),
                        title: '摄像头$index',
                        showDivider: false,
                      )).toList();

              return TDRadioGroup(
                selectId: state.selectedCameraId.toString(),
                direction: Axis.vertical,
                directionalTdRadios: radios,
                onRadioGroupChange: (selected) {
                  final selectedCamera = int.tryParse(selected ?? '');
                  if (selectedCamera != null) {
                    wyDeviceBloc.add(SelectedCameraId(selectedCamera));
                  }
                },
              );
            })),
            const SizedBox(
              height: 12,
            ),
            _buildOptionRow(
              Text("显示ROI"),
              TDSwitch(
                isOn: _showROIs,
                onChanged: (value) {
                  setState(() {
                    _showROIs = !_showROIs;
                  });
                  return false;
                },
              ),
            ),
            SizedBox(
              height: 12,
            ),
            _buildOptionRow(
              const Text("PW"),
              Row(children: [
                const SizedBox(
                  width: 12,
                ),
                Expanded(
                  child: TDSlider(
                    sliderThemeData: TDSliderThemeData(
                      context: context,
                      showThumbValue: true,
                      scaleFormatter: (value) {
                        return value.toInt().toString();
                      },
                      min: 0,
                      max: 10000,
                    ),
                    value: (wyDeviceBloc.state.deviceAttribute?.pwm ?? 2000)
                        .toDouble(),
                    rightLabel: '10000',
                    onChanged: (value) {
                      _pwm = value.toInt();
                    },
                  ),
                ),
                TextButton(
                  child: const Text("发送"),
                  onPressed: () {
                    // note : 服务端返回 200 ，但实际上并不生效
                    context
                        .read<WyDeviceBloc>()
                        .add(UpdateSingleAttribute('pwm', _pwm));
                  },
                )
              ]),
            ),
            const Center(child: TDDivider()),
            Center(
                child: TDButton(
              text: '请求图像',
              size: TDButtonSize.large,
              type: TDButtonType.fill,
              shape: TDButtonShape.rectangle,
              theme: TDButtonTheme.primary,
              onTap: () {
                getLatestPicture(wyDeviceBloc.state.selectedCameraId);
              },
            ))
          ],
        ));
  }

  Widget _buildOptionRow(Widget labelWidget, Widget inputWidget) {
    return Row(
      children: [
        Expanded(
            flex: 1,
            child: Align(
              alignment: Alignment.centerRight,
              child: labelWidget,
            )),
        const SizedBox(
          width: 12,
        ),
        Expanded(
            flex: 4,
            child: Align(
              alignment: Alignment.centerLeft,
              child: inputWidget,
            )),
      ],
    );
  }

  /// 历史图片查看面板，可以选择日期和时间
  Widget _buildHistoryPanel() {
    return Row(
      children: [
        Expanded(
          child: GestureDetector(
            child: Transform(
              alignment: Alignment.center,
              transform: Matrix4.diagonal3Values(-1.0, 1.0, 1.0), // x 轴缩放为 -1
              child: const Icon(
                TDIcons.next_filled,
                color: Colors.blueAccent,
              ),
            ),
            onTap: () async {
              final history = await getCameraImageHistory(
                  wyDeviceBloc.state.selectedCameraId);
              final idx = min(
                  lowerBound(history, _dateTime.millisecondsSinceEpoch),
                  history.length - 1);
              // history 为空时，index 为-1
              if (idx != -1 && idx - 1 >= 0) {
                setState(() {
                  _dateTime =
                      DateTime.fromMillisecondsSinceEpoch(history[idx - 1]);
                });
              }
            },
          ),
        ),
        const SizedBox(
          width: 24,
        ),
        TDButton(
            text: '${_dateTime.year}'
                '/${_dateTime.month.toString().padLeft(2, '0')}'
                '/${_dateTime.day.toString().padLeft(2, '0')}',
            size: TDButtonSize.small,
            type: TDButtonType.fill,
            shape: TDButtonShape.rectangle,
            theme: TDButtonTheme.light,
            onTap: () {
              _showPickDateDialog(context);
            }),
        const SizedBox(
          width: 24,
        ),
        TDButton(
          text: '${_dateTime.hour.toString().padLeft(2, '0')}'
              ':${_dateTime.minute.toString().padLeft(2, '0')}'
              ':${_dateTime.second.toString().padLeft(2, '0')}',
          size: TDButtonSize.small,
          type: TDButtonType.fill,
          shape: TDButtonShape.rectangle,
          theme: TDButtonTheme.light,
          onTap: () {
            _showPickPictureDialog(context);
          },
        ),
        const SizedBox(
          width: 24,
        ),
        Expanded(
          child: GestureDetector(
            child: const Icon(
              TDIcons.next_filled,
              color: Colors.blueAccent,
            ),
            onTap: () async {
              final history = await getCameraImageHistory(
                  wyDeviceBloc.state.selectedCameraId);
              final idx = min(
                  lowerBound(history, _dateTime.millisecondsSinceEpoch),
                  history.length - 1);
              // history 为空时，index 为-1
              if (idx != -1 && idx + 1 < history.length) {
                setState(() {
                  _dateTime =
                      DateTime.fromMillisecondsSinceEpoch(history[idx + 1]);
                });
              }
            },
          ),
        )
      ],
    );
  }

  Widget _buildSwitchableSection(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final targetBloc = context.read<TargetBloc>();

    return Container(
      height: 320, // 固定合适的高度
      width: screenWidth, // 使用全屏宽度
      child: LayoutBuilder(
        builder: (context, constraints) {
          return Container(
              width: constraints.maxWidth,
              height: constraints.maxHeight,
              child: BlocBuilder<WyDeviceBloc, WyDeviceState>(
                  builder: (context, state) {
                final cameraId = state.selectedCameraId;
                final roiRects = targetBloc.state.targets
                    .where((target) => target.cameraId == cameraId)
                    .map((target) {
                  return {
                    'rect': target.rect,
                    'label': target.name,
                    'id': target.targetId,
                  };
                }).toList();

                return CustomPaint(
                  painter: ROIPainter(
                    image: _currentImage,
                    imageDisplayRect: Rect.fromLTWH(
                        0,
                        0,
                        // screenWidth,
                        // 320,
                        // _sampleImage?.width.toDouble() ??
                        constraints.maxWidth,
                        // _sampleImage?.height.toDouble() ??
                        constraints.maxHeight),
                    roiRects: roiRects,
                    showTitles: true,
                    showROIs: _showROIs,
                  ),
                );
              }));
        },
      ),
    );
  }

  void _showPickDateDialog(BuildContext context) {
    debugPrint("_showSyncDialog-1");

    final size = MediaQuery.of(context).size;
    TDCalendarPopup(
      context,
      visible: true,
      onConfirm: (value) {
        final timestamp = value.firstOrNull;
        if (timestamp != null) {
          setState(() {
            _dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
          });
        }
      },
      child: TDCalendar(
        title: '请选择日期',
        height: size.height * 0.6 + 176,
        minDate: (_imageHistory[wyDeviceBloc.state.selectedCameraId ?? 0] ?? [])
                .firstOrNull ??
            DateTime(2025, 6, 1).millisecondsSinceEpoch,
        maxDate: (_imageHistory[wyDeviceBloc.state.selectedCameraId ?? 0] ?? [])
                .lastOrNull ??
            DateTime(2026, 6, 1).millisecondsSinceEpoch,
        format: (day) {
          if (day == null) {
            return null;
          }
          TextStyle? style = const TextStyle(color: Colors.grey);
          try {
            final history =
                _imageHistory[wyDeviceBloc.state.selectedCameraId ?? 0] ?? [];
            final start = day.date.millisecondsSinceEpoch;
            final end = day.date
                .copyWith()
                .add(const Duration(days: 1))
                .millisecondsSinceEpoch;
            final idx = lowerBound(history, start);
            if (idx < history.length && history[idx] < end) {
              style = null;
            }
          } catch (e) {}
          if (style != null) {
            day.style = style;
          }
          return null;
        },
      ),
    );
  }

  void _showPickPictureDialog(BuildContext context) {
    final history =
        _imageHistory[wyDeviceBloc.state.selectedCameraId ?? 0] ?? [];

    final start = _dateTime.copyWith(
        hour: 0, minute: 0, second: 0, millisecond: 0, microsecond: 0);
    final end = start.copyWith().add(const Duration(days: 1));

    final dayHistory = history
        .where((p) =>
            p >= start.millisecondsSinceEpoch && p < end.millisecondsSinceEpoch)
        .toList();

    TDPicker.showMultiPicker(context, title: '选择时间', initialIndexes: [
      min(lowerBound(dayHistory, _dateTime.millisecondsSinceEpoch),
          dayHistory.length - 1)
    ], onConfirm: (selected) {
      setState(() {
        _dateTime =
            DateTime.fromMillisecondsSinceEpoch(dayHistory[selected.first]);
      });
      Navigator.of(context).pop();
    }, data: [
      dayHistory.map((p) {
        final dateTime = DateTime.fromMillisecondsSinceEpoch(p);
        return "${dateTime.hour}:${dateTime.minute}:${dateTime.second}";
      }).toList()
    ]);
  }
}

Future<void> getLatestPicture(int cameraId) async {
  try {
    // 创建请求摄像头的任务
    // 返回值通过 websocket 回调获取
    final result = await HttpUtil.instance.client.post(
      '/rpc',
      data: json.encode({
        "method": "wyGetLatestPicture",
        "params": {
          "cameraId": cameraId,
          "resizeX": 640,
          "resizeY": 480,
          "inverted": false,
          "showROI": false,
        }
      }),
    );
  } catch (e) {
    debugPrint("获取图像失败:$e , 摄像头 : $cameraId");
  }
}
